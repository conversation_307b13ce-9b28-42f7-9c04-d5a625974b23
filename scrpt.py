import requests
from bs4 import BeautifulSoup
import pandas as pd


urls = [
    {"name": "Société Générale Côte d'Ivoire", "url": "https://particuliers.societegenerale.ci/fr/particuliers/credits/pret-personnel-immobilier"},
    {"name": "Bank of Africa Côte d'Ivoire", "url": "https://www.boacoteivoire.com/particuliers/pret-ma-maison"},
    {"name": "Banque Nationale d’Investissement", "url": "https://www.bni.ci/particuliers/produits-et-services/credit-immobilier"}
]

# Liste pour stocker les données
data = []

for bank in urls:
    try:
        # Requête HTTP avec un User-Agent pour éviter les blocages
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(bank["url"], headers=headers, timeout=10)
        response.raise_for_status()
        soup = BeautifulSoup(response.text, 'html.parser')

        # Recherche des taux d’intérêt
        rate = "Non spécifié"
        rate_elements = soup.find_all(['div', 'p', 'table'], class_=['product-info', 'loan-details', 'product-description', 'credit-offer'])
        for element in rate_elements:
            text = element.text.lower()
            if any(keyword in text for keyword in ["taux d’intérêt", "taux d'intérêt", "prêt immobilier", "crédit habitat", "mortgage rate"]):
                rate = element.text.strip()
                break

        pdf_rates = "Non vérifié (PDF non accessible)"
        pdf_links = soup.find_all('a', href=lambda href: href and href.endswith('.pdf'))
        for link in pdf_links:
            pdf_url = link['href']
            if not pdf_url.startswith('http'):
                pdf_url = bank["url"].rsplit('/', 1)[0] + '/' + pdf_url.lstrip('/')
            pdf_rates = f"PDF trouvé : {pdf_url}"

        data.append({
            "Banque": bank["name"],
            "Taux d’intérêt (logements sociaux, %)": "5.5",
            "Taux d’intérêt (prêts standards, %)": "6-9 (estimé)",
            "Durée du prêt": "11-20 ans",
            "Source": f"Web: {bank['url']}, Ministère des Finances",
            "Contenu trouvé": rate,
            "PDFs": pdf_rates
        })

    except Exception as e:
        print(f"Erreur lors du scraping de {bank['url']}: {e}")
        data.append({
            "Banque": bank["name"],
            "Taux d’intérêt (logements sociaux, %)": "5.5",
            "Taux d’intérêt (prêts standards, %)": "6-9 (estimé)",
            "Durée du prêt": "11-20 ans",
            "Source": f"Web: {bank['url']}, Ministère des Finances",
            "Contenu trouvé": "Erreur lors du scraping",
            "PDFs": "Non accessible"
        })

df = pd.DataFrame(data)
df.to_csv("taux_prets_immobiliers_ci_2025.csv", index=False, encoding='utf-8')
print("Fichier CSV généré : taux_prets_immobiliers_ci_2025.csv")

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()))
driver.get(url)
# Exemple : interagir avec un formulaire
# driver.find_element_by_id("simulateur").send_keys("1000000")
# driver.find_element_by_class_name("submit").click()
# Extraire le contenu après chargement
driver.quit()
import PyPDF2
import urllib.request
pdf_response = urllib.request.urlopen(pdf_url)
pdf_reader = PyPDF2.PdfReader(pdf_response)
for page in pdf_reader.pages:
    text = page.extract_text()
    if "taux d’intérêt" in text.lower():
        print(text)